/* react-image-crop styles */
.ReactCrop {
  position: relative;
  display: block;
  cursor: crosshair;
  width: 100%;
  background: transparent;
  overflow: hidden; /* 限制遮罩范围 */
}

.ReactCrop *,
.ReactCrop *::before,
.ReactCrop *::after {
  box-sizing: border-box;
}

.ReactCrop__image {
  display: block;
  max-width: 100%;
  max-height: 100%;
  height: auto;
}

.ReactCrop__crop-selection {
  position: absolute;
  top: 0;
  left: 0;
  transform: translate3d(0, 0, 0);
  box-sizing: border-box;
  cursor: move;
  /* 修改遮罩实现，只在图片区域内生效 */
  box-shadow:
    0 0 0 1000px rgba(0, 0, 0, 0.3),
    inset 0 0 0 2px rgba(59, 130, 246, 0.8);
  border: none; /* 移除边框，使用 inset box-shadow 代替 */
  touch-action: manipulation;
}

.ReactCrop__crop-selection:focus {
  outline: none;
}

.ReactCrop__crop-selection--circular-crop {
  border-radius: 50% !important;
  /* 使用 inset box-shadow 来创建圆形边框 */
  box-shadow:
    0 0 0 1000px rgba(0, 0, 0, 0.3),
    inset 0 0 0 3px rgba(59, 130, 246, 0.9) !important;
}

.ReactCrop__drag-handle {
  position: absolute;
  width: 12px;
  height: 12px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.2);
  box-sizing: border-box;
  outline: 1px solid transparent;
}

.ReactCrop__drag-handle::after {
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  content: '';
  cursor: inherit;
}

.ReactCrop__drag-handle--ord-nw {
  top: -6px;
  left: -6px;
  cursor: nw-resize;
}

.ReactCrop__drag-handle--ord-n {
  top: -6px;
  left: 50%;
  margin-left: -6px;
  cursor: n-resize;
}

.ReactCrop__drag-handle--ord-ne {
  top: -6px;
  right: -6px;
  cursor: ne-resize;
}

.ReactCrop__drag-handle--ord-e {
  top: 50%;
  right: -6px;
  margin-top: -6px;
  cursor: e-resize;
}

.ReactCrop__drag-handle--ord-se {
  bottom: -6px;
  right: -6px;
  cursor: se-resize;
}

.ReactCrop__drag-handle--ord-s {
  bottom: -6px;
  left: 50%;
  margin-left: -6px;
  cursor: s-resize;
}

.ReactCrop__drag-handle--ord-sw {
  bottom: -6px;
  left: -6px;
  cursor: sw-resize;
}

.ReactCrop__drag-handle--ord-w {
  top: 50%;
  left: -6px;
  margin-top: -6px;
  cursor: w-resize;
}

.ReactCrop__drag-handle--disabled {
  cursor: inherit;
}

.ReactCrop__rule-of-thirds-hz::before,
.ReactCrop__rule-of-thirds-hz::after,
.ReactCrop__rule-of-thirds-vt::before,
.ReactCrop__rule-of-thirds-vt::after {
  content: '';
  position: absolute;
  background: rgba(255, 255, 255, 0.4);
}

.ReactCrop__rule-of-thirds-vt::before,
.ReactCrop__rule-of-thirds-vt::after {
  width: 1px;
  height: 100%;
}

.ReactCrop__rule-of-thirds-vt::before {
  left: 33.333%;
}

.ReactCrop__rule-of-thirds-vt::after {
  left: 66.666%;
}

.ReactCrop__rule-of-thirds-hz::before,
.ReactCrop__rule-of-thirds-hz::after {
  width: 100%;
  height: 1px;
}

.ReactCrop__rule-of-thirds-hz::before {
  top: 33.333%;
}

.ReactCrop__rule-of-thirds-hz::after {
  top: 66.666%;
}

/* 自定义样式 */
.ReactCrop__crop-selection--circular-crop .ReactCrop__drag-handle {
  background: rgba(59, 130, 246, 0.9);
  border: 2px solid rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 14px;
  height: 14px;
}

/* 移除重复的样式定义和导致双重边框的伪元素 */

/* 响应式调整 */
@media (max-width: 768px) {
  .ReactCrop__drag-handle {
    width: 16px;
    height: 16px;
  }
  
  .ReactCrop__drag-handle--ord-nw {
    top: -8px;
    left: -8px;
  }
  
  .ReactCrop__drag-handle--ord-n {
    top: -8px;
    margin-left: -8px;
  }
  
  .ReactCrop__drag-handle--ord-ne {
    top: -8px;
    right: -8px;
  }
  
  .ReactCrop__drag-handle--ord-e {
    right: -8px;
    margin-top: -8px;
  }
  
  .ReactCrop__drag-handle--ord-se {
    bottom: -8px;
    right: -8px;
  }
  
  .ReactCrop__drag-handle--ord-s {
    bottom: -8px;
    margin-left: -8px;
  }
  
  .ReactCrop__drag-handle--ord-sw {
    bottom: -8px;
    left: -8px;
  }
  
  .ReactCrop__drag-handle--ord-w {
    left: -8px;
    margin-top: -8px;
  }
}
